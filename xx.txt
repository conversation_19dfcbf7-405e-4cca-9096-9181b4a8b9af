Integration Developer Assignment – Web App Challenge
Objective
Create a web application that demonstrates your ability to work with the Apify platform: fetch available actors, expose their schema at runtime, and execute one actor run based on user-provided inputs, surfacing the result immediately.
What to Build
Web Frontend
A simple interface where a user can authenticate with their Apify API key and choose one of their actors.
Once chosen, present the actor's input schema for the user to supply values.
Provide a way for the user to trigger a single execution and see the outcome.
Backend Integration
Communicate securely with Apify's API to list actors and retrieve schemas on demand.
Execute the actor with the user's inputs and return the run result to the frontend.
Key Requirements
Dynamic Schema Loading
Schemas must be fetched at runtime for whatever actor the user selects—no pre-stored or hardcoded definitions.
Single-Run Execution
The app should perform exactly one actor execution per request and immediately present the result or any error details.
Error Handling & Feedback
Users should receive clear feedback if something goes wrong (e.g., invalid key, schema mismatch, execution failure).
Minimal Dependencies
Focus on a clean, straightforward solution rather than elaborate frameworks or tooling.
Deliverables
Source Code
Frontend and backend in a public repo or zipped archive.
README
How to install and run your application.
Which actor you chose for testing.
Any assumptions or notable design choices you made.
Screenshots or brief notes demonstrating the working flow.
What We’re Looking For
Product Thinking: Clear, intuitive user flow without exhaustive instructions.
Technical Rigor: Secure API use, dynamic schema handling, proper error propagation.
Code Quality: Organized, maintainable code with concise documentation.
Creativity: Thoughtful UI/UX and any enhancements beyond the bare minimum.
Summary of What You Need to Make
You need to build a simple web application with a frontend and a backend that lets a user run one of their Apify actors.
The user flow should be:
The user enters their Apify API key.
The app uses the key to fetch and display a list of the user's actors from the Apify API.
The user selects an actor from the list.
The app fetches the input schema for that specific actor and dynamically generates a form for the user to fill out.
The user fills the form and clicks a "run" button.
The app executes the actor with the provided inputs and immediately displays the results or any errors to the user.
Your final submission must include all source code and a README file explaining how to run it and your design choices.