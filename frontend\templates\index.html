<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apify Runner</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="app">
        <header>
            <h1>Apify Runner</h1>
            <p>A simple UI to run your Apify actors.</p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 25%;"></div>
            </div>
        </header>

        <!-- Step 1: API Token -->
        <div id="auth-step" class="step active">
            <h2>Step 1: Authenticate</h2>
            <div class="form-group">
                <input type="password" id="api-token" placeholder="Enter your Apify API token (apify_api_...)" autocomplete="off">
                <button onclick="authenticate()">Validate Token</button>
            </div>
            <div id="auth-status" class="status"></div>
        </div>

        <!-- Step 2: Select Actor -->
        <div id="actors-step" class="step">
            <h2>Step 2: Select an Actor</h2>
            <div id="actors-grid"></div>
            <div id="actors-status" class="status"></div>
        </div>

        <!-- Step 3: Configure Input -->
        <div id="config-step" class="step">
            <h2>Step 3: Configure Input</h2>
            <div id="selected-actor-info" class="actor-info"></div>
            <form id="input-form" onsubmit="event.preventDefault(); executeActor();">
                <div id="form-fields"></div>
                <button type="submit">Run Actor</button>
            </form>
            <div id="config-status" class="status"></div>
        </div>

        <!-- Step 4: View Results -->
        <div id="results-step" class="step">
            <h2>Step 4: Results</h2>
            <div class="results-header">
                <div id="run-status" class="run-status"></div>
                <button onclick="restart()" class="restart-btn">Start Over</button>
            </div>
            <div id="results-content" class="results-content"></div>
        </div>

        <!-- Global Loader -->
        <div id="loader" class="loader">
            <div class="spinner"></div>
        </div>
        
        <footer>
            <div id="session-stats" style="display: none;">
                Session: <span id="session-duration">0m 0s</span> | 
                Actors Viewed: <span id="actors-viewed">0</span> | 
                Runs Executed: <span id="runs-executed">0</span>
            </div>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>