import requests
import re
import logging

logger = logging.getLogger(__name__)

class ApifyClient:
    """A simple client for the Apify API."""
    def __init__(self, api_token):
        if not isinstance(api_token, str) or not api_token.strip():
            raise ValueError("Apify API token must be a non-empty string.")
            
        self.token = api_token.strip()
        self.base_url = "https://api.apify.com/v2"
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            "User-Agent": "ApifyRunner/1.0"
        }
    
    def _is_valid_actor_id(self, actor_id):
        """Basic check for valid actor ID format (e.g., username/actor-name)."""
        if not isinstance(actor_id, str):
            return False
        return bool(re.match(r'^[a-zA-Z0-9_~-]+/[a-zA-Z0-9_~-]+$', actor_id.strip()))

    def _handle_request(self, method, url, **kwargs):
        """Wrapper for requests to handle common exceptions."""
        try:
            return requests.request(method, url, headers=self.headers, **kwargs)
        except requests.exceptions.Timeout:
            return {"success": False, "error": "Request timed out. Please try again."}
        except requests.exceptions.ConnectionError:
            return {"success": False, "error": "Connection error. Check your internet."}
        except Exception as e:
            logger.error(f"Unexpected request error: {e}")
            return {"success": False, "error": "An unexpected error occurred."}

    def test_connection(self):
        """Tests the API token by fetching user info."""
        response = self._handle_request("get", f"{self.base_url}/users/me", timeout=10)
        if isinstance(response, dict): return response # Error from _handle_request

        if response.status_code == 200:
            return {"success": True}
        if response.status_code == 401:
            return {"success": False, "error": "Invalid API token."}
        return {"success": False, "error": f"API error (status {response.status_code})"}

    def list_actors(self):
        """Fetches a list of the user's actors."""
        response = self._handle_request("get", f"{self.base_url}/acts", timeout=15)
        if isinstance(response, dict): return response

        if response.status_code == 200:
            items = response.json().get("data", {}).get("items", [])
            logger.info(f"Successfully fetched {len(items)} actors")
            return {"success": True, "actors": items}
        
        return {"success": False, "error": f"Failed to fetch actors (status {response.status_code})"}

    def get_actor_details(self, actor_id):
        """Fetches details and input schema for a specific actor."""
        if not self._is_valid_actor_id(actor_id):
            return {"success": False, "error": "Invalid actor ID format. Should be 'username/actor-name'."}

        # Fetch main actor details first
        actor_response = self._handle_request("get", f"{self.base_url}/acts/{actor_id}", timeout=10)
        if isinstance(actor_response, dict): return actor_response

        if actor_response.status_code != 200:
            if actor_response.status_code == 404: return {"success": False, "error": "Actor not found."}
            return {"success": False, "error": f"Failed to get actor details (status {actor_response.status_code})"}
        
        actor_data = actor_response.json().get("data", {})

        # Then, try to get the input schema. It's okay if this fails.
        schema_response = self._handle_request("get", f"{self.base_url}/acts/{actor_id}/versions/latest", timeout=10)
        if not isinstance(schema_response, dict) and schema_response.status_code == 200:
            schema = schema_response.json().get("data", {}).get("inputSchema")
            if schema:
                actor_data["inputSchema"] = schema
                logger.info(f"Schema loaded for actor: {actor_id}")

        return {"success": True, "actor": actor_data}

    def run_actor(self, actor_id, input_data):
        """Starts an actor run and waits for it to finish."""
        if not self._is_valid_actor_id(actor_id):
            return {"success": False, "error": "Invalid actor ID format."}
        if not isinstance(input_data, dict):
            return {"success": False, "error": "Input data must be a dictionary."}
            
        logger.info(f"Starting actor run: {actor_id}")
        # We wait for 120s, so the request timeout needs to be slightly longer.
        response = self._handle_request(
            "post",
            f"{self.base_url}/acts/{actor_id}/runs",
            json=input_data,
            params={"waitForFinish": 120},
            timeout=130
        )
        if isinstance(response, dict): return response

        if response.status_code == 201:
            run_data = response.json().get("data", {})
            logger.info(f"Actor run finished: {actor_id}, status: {run_data.get('status')}")
            return {"success": True, "run": run_data}
        
        error_map = {
            404: "Actor not found.",
            403: "Access denied to this actor.",
            400: "Invalid input data provided."
        }
        error_msg = error_map.get(response.status_code, f"Run failed (HTTP {response.status_code})")
        return {"success": False, "error": error_msg}


    def get_dataset_items(self, dataset_id):
        """Fetches all items from a given dataset."""
        logger.info(f"Fetching dataset items: {dataset_id}")
        response = self._handle_request("get", f"{self.base_url}/datasets/{dataset_id}/items", timeout=20)
        if isinstance(response, dict): return response

        if response.status_code == 200:
            items = response.json()
            logger.info(f"Successfully fetched {len(items)} items from dataset {dataset_id}")
            return {"success": True, "items": items}

        return {"success": False, "error": f"Failed to fetch dataset items (status {response.status_code})"}