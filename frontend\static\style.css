/* 
 * A simple, dark theme for the Apify Runner
 */

:root {
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --border-color: #333;
    --text-primary: #cccccc;
    --text-secondary: #999;
    --accent-color: #00ff41;
    --accent-hover: #00cc33;
    --error-color: #ff4444;
    --border-radius: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}


/* --- Layout & Typography --- */

.app {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    text-align: center;
    margin-bottom: 3rem;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

h2 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 300;
}

footer {
    margin-top: 2rem;
    padding: 1rem;
    text-align: center;
    font-size: 0.8rem;
    color: var(--text-secondary);
    border-top: 1px solid var(--border-color);
}

/* --- Steps & Transitions --- */

.step {
    display: none;
    animation: fadeIn 0.3s ease;
}

.step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    margin: 1rem 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-hover));
    transition: width 0.3s ease-out;
}


/* --- Forms & Buttons --- */

.form-group {
    display: flex;
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.field-group {
    margin-bottom: 1.5rem;
}

.field-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

input[type="text"],
input[type="password"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1rem;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 255, 65, 0.1);
}

button {
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    background: var(--accent-color);
    color: var(--bg-primary);
}

button:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

button.restart-btn {
    background: var(--border-color);
    color: var(--text-primary);
}

button.restart-btn:hover {
    background: #444;
}


/* --- Components --- */

.status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid transparent;
}

.status.success {
    background: rgba(0, 255, 65, 0.1);
    color: var(--accent-color);
    border-color: rgba(0, 255, 65, 0.3);
}

.status.error {
    background: rgba(255, 0, 0, 0.1);
    color: var(--error-color);
    border-color: rgba(255, 0, 0, 0.3);
}

.actors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.actor-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.actor-card:hover {
    border-color: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.actor-card h3 {
    color: white;
    margin-bottom: 0.5rem;
}

.actor-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.actor-info {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.run-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.run-status.success { background: rgba(0, 255, 65, 0.2); color: var(--accent-color); }
.run-status.failed { background: rgba(255, 0, 0, 0.2); color: var(--error-color); }

.results-content pre {
    background: #000;
    padding: 1rem;
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-family: 'SF Mono', Monaco, monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 500px;
    overflow-y: auto;
}

.results-content dl {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 0.5rem 1.5rem;
}

.results-content dt { font-weight: bold; color: var(--text-secondary); }
.results-content dd { color: white; }


/* --- Loader & Utilities --- */

.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 15, 35, 0.85);
    display: none; /* Toggled by JS */
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.loader.active { display: flex; }

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top-color: var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 220px;
    background-color: var(--bg-secondary);
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -110px;
    opacity: 0;
    transition: opacity 0.3s;
    border: 1px solid var(--border-color);
    font-size: 0.8rem;
    font-weight: normal;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* --- Responsive --- */
@media (max-width: 768px) {
    .app { padding: 1rem; }
    header h1 { font-size: 2rem; }
    .form-group { flex-direction: column; }
    .actors-grid { grid-template-columns: 1fr; }
}