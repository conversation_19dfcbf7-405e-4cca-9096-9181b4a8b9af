# Architecture Overview

This document explains the architecture and design decisions of the Apify Actor Runner application.

## High-Level Architecture

```
┌─────────────────┐    HTTP/JSON    ┌─────────────────┐    HTTPS/JSON    ┌─────────────────┐
│                 │ ◄──────────────► │                 │ ◄───────────────► │                 │
│   Web Browser   │                 │  Flask Backend  │                  │   Apify API     │
│  (Frontend)     │                 │   (Python)      │                  │                 │
│                 │                 │                 │                  │                 │
└─────────────────┘                 └─────────────────┘                  └─────────────────┘
```

## Component Breakdown

### Frontend (Browser)
- **Technology**: Vanilla HTML5, CSS3, JavaScript (ES6+)
- **Files**: `templates/index.html`, `static/style.css`, `static/script.js`
- **Responsibilities**:
  - User interface rendering
  - Form validation and submission
  - API communication
  - Dynamic content generation
  - Error display and user feedback

### Backend (Flask Server)
- **Technology**: Python 3.7+, Flask 3.0.0
- **Files**: `app.py`, `apify_client.py`
- **Responsibilities**:
  - HTTP request routing
  - API token validation
  - Apify API integration
  - Data transformation
  - Error handling and logging

### External API (Apify)
- **Technology**: REST API over HTTPS
- **Endpoints Used**:
  - `/v2/users/me` - Token validation
  - `/v2/acts` - List actors
  - `/v2/acts/{id}` - Get actor details
  - `/v2/acts/{id}/runs` - Run actor
  - `/v2/datasets/{id}/items` - Get results

## Data Flow

### 1. Token Validation Flow
```
User Input → Frontend Validation → POST /api/test-token → ApifyClient.test_connection() → Apify API → Response Chain
```

### 2. Actor Discovery Flow
```
Valid Token → POST /api/actors → ApifyClient.list_actors() → Apify API → Actor List → Frontend Display
```

### 3. Schema Loading Flow
```
Actor Selection → POST /api/actor/{id}/schema → ApifyClient.get_actor_details() → Schema Extraction → Dynamic Form Generation
```

### 4. Actor Execution Flow
```
Form Submission → Input Validation → POST /api/actor/{id}/run → ApifyClient.run_actor() → Apify API → Results Retrieval → Display
```

## Design Patterns

### 1. Client-Server Pattern
- Clear separation between frontend (client) and backend (server)
- RESTful API communication
- Stateless server design

### 2. Adapter Pattern
- `ApifyClient` class adapts Apify's REST API to Python methods
- Consistent error handling across all API calls
- Simplified interface for complex API operations

### 3. Progressive Enhancement
- Basic HTML structure works without JavaScript
- CSS provides visual enhancement
- JavaScript adds interactivity and dynamic behavior

### 4. Error-First Design
- All API calls return success/error status
- Comprehensive error handling at every level
- User-friendly error messages

## Security Architecture

### Authentication
- **No server-side storage**: API tokens never stored on server
- **Request-based auth**: Token included in each API request
- **Validation**: Token validated before any operations

### Input Validation
- **Frontend validation**: Immediate user feedback
- **Backend validation**: Server-side security checks
- **Type checking**: Proper data type validation

### Error Handling
- **Information disclosure**: Error messages don't expose sensitive data
- **Graceful degradation**: Application continues working despite errors
- **Logging**: Errors logged for debugging (without sensitive data)

## Performance Considerations

### Frontend Optimization
- **Minimal dependencies**: No external JavaScript libraries
- **Efficient DOM manipulation**: Direct element access
- **Lazy loading**: Steps revealed progressively
- **Responsive design**: Mobile-friendly interface

### Backend Optimization
- **Connection reuse**: HTTP session management
- **Timeout handling**: Reasonable timeouts for API calls
- **Memory efficiency**: Minimal data storage
- **Error caching**: Quick failure for invalid tokens

### API Integration
- **Synchronous runs**: Uses `waitForFinish` parameter
- **Timeout management**: 2-minute wait for actor completion
- **Rate limit respect**: Follows Apify's rate limiting

## Scalability Considerations

### Current Limitations
- **Single-user design**: No multi-user support
- **No persistence**: No database or session storage
- **Synchronous processing**: Blocks during actor runs
- **Memory bound**: All data processed in memory

### Potential Improvements
- **Database integration**: Store user sessions and run history
- **Async processing**: Background job processing
- **Caching**: Cache actor schemas and metadata
- **Load balancing**: Multiple server instances

## File Structure Rationale

```
├── app.py                 # Main application entry point
├── apify_client.py        # API client abstraction
├── requirements.txt       # Dependency management
├── .env                   # Configuration (optional)
├── templates/
│   └── index.html        # Single-page application template
├── static/
│   ├── style.css         # Styling (mobile-first responsive)
│   └── script.js         # Client-side logic
└── docs/
    ├── API.md            # API documentation
    ├── ARCHITECTURE.md   # This file
    └── TROUBLESHOOTING.md # Support documentation
```

### Design Rationale
- **Flat structure**: Easy to understand and navigate
- **Separation of concerns**: Clear boundaries between components
- **Documentation co-location**: Docs alongside code
- **Standard conventions**: Follows Flask and web standards

## Technology Choices

### Why Flask?
- **Simplicity**: Minimal boilerplate for small applications
- **Flexibility**: Easy to customize and extend
- **Python ecosystem**: Rich libraries for API integration
- **Development speed**: Quick prototyping and iteration

### Why Vanilla JavaScript?
- **No dependencies**: Reduces complexity and load time
- **Full control**: Complete control over behavior
- **Browser compatibility**: Works across all modern browsers
- **Learning value**: Demonstrates core web technologies

### Why No Database?
- **Simplicity**: Reduces setup and maintenance
- **Stateless design**: Each request is independent
- **Security**: No persistent data storage concerns
- **Demonstration focus**: Emphasizes API integration

## Extension Points

### Adding Features
1. **User authentication**: Add login/logout functionality
2. **Run history**: Store and display previous runs
3. **Batch operations**: Run multiple actors simultaneously
4. **Scheduling**: Schedule actor runs for later
5. **Webhooks**: Receive notifications when runs complete

### Integration Options
1. **Database**: Add PostgreSQL/SQLite for persistence
2. **Queue system**: Add Redis/Celery for background jobs
3. **Monitoring**: Add logging and metrics collection
4. **Deployment**: Docker containerization and cloud deployment

## Testing Strategy

### Current Testing
- **Manual testing**: User flow validation
- **Error simulation**: Network and API error handling
- **Browser testing**: Cross-browser compatibility

### Recommended Testing
- **Unit tests**: Test individual functions and classes
- **Integration tests**: Test API endpoints
- **End-to-end tests**: Automated user flow testing
- **Performance tests**: Load and stress testing
