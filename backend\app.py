from flask import Flask, render_template, request, jsonify, g
from apify_client import ApifyClient
from functools import wraps
import time
import logging

app = Flask(__name__, template_folder='../frontend/templates', static_folder='../frontend/static')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# NOTE: This in-memory rate limiting is simple, but won't work across multiple server processes.
# For production, a shared store like Redis would be better.
request_timestamps = {}

def rate_limit_check(client_ip, max_requests=30, window_seconds=60):
    """Simple rate limiting: max_requests per window_seconds per IP."""
    now = time.time()
    if client_ip not in request_timestamps:
        request_timestamps[client_ip] = []
    
    # Ditch old timestamps
    valid_stamps = [ts for ts in request_timestamps[client_ip] if now - ts < window_seconds]
    request_timestamps[client_ip] = valid_stamps
    
    if len(valid_stamps) >= max_requests:
        return False
    
    request_timestamps[client_ip].append(now)
    return True

# A decorator to handle token validation and client creation
def with_apify_client(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_ip = request.remote_addr
        if not rate_limit_check(client_ip):
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return jsonify({"success": False, "error": "Too many requests. Please try again later."}), 429

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Invalid request body."}), 400

        token = data.get('api_token', '').strip()
        if not token:
            return jsonify({"success": False, "error": "API token is required."}), 401
        
        try:
            # Store the client in Flask's 'g' object for this request
            g.client = ApifyClient(token)
            g.data = data
        except ValueError as e:
            return jsonify({"success": False, "error": str(e)}), 400
            
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/test-token', methods=['POST'])
@with_apify_client
def test_token():
    logger.info(f"Testing API token for IP: {request.remote_addr}")
    result = g.client.test_connection()
    status_code = 200 if result["success"] else 401
    return jsonify(result), status_code

@app.route('/api/actors', methods=['POST'])
@with_apify_client
def get_actors():
    logger.info(f"Fetching actors for IP: {request.remote_addr}")
    result = g.client.list_actors()
    status_code = 200 if result["success"] else 400
    return jsonify(result), status_code

@app.route('/api/actor/<actor_id>/schema', methods=['POST'])
@with_apify_client
def get_actor_schema(actor_id):
    logger.info(f"Fetching schema for actor {actor_id} for IP: {request.remote_addr}")
    result = g.client.get_actor_details(actor_id)

    if not result["success"]:
        return jsonify(result), 400

    # Try to find the input schema from a few common places in the actor object
    actor = result["actor"]
    schema = actor.get("inputSchema") or actor.get("input")
    
    if not schema:
        logger.info(f"No input schema found for actor {actor_id}, sending empty schema.")
        return jsonify({
            "success": True, 
            "schema": {"type": "object", "properties": {}, "description": "No input schema available."}
        })

    return jsonify({"success": True, "schema": schema})

@app.route('/api/actor/<actor_id>/run', methods=['POST'])
@with_apify_client
def run_actor(actor_id):
    input_data = g.data.get('input', {})
    logger.info(f"Running actor {actor_id} for IP: {request.remote_addr}")
    
    run_result = g.client.run_actor(actor_id, input_data)
    if not run_result["success"]:
        return jsonify(run_result), 400

    run_data = run_result["run"]
    items = []

    # If the run succeeded and gave us a dataset, go fetch the results
    dataset_id = run_data.get("defaultDatasetId")
    if run_data.get("status") == "SUCCEEDED" and dataset_id:
        logger.info(f"Run succeeded, fetching results from dataset {dataset_id}")
        dataset_result = g.client.get_dataset_items(dataset_id)
        if dataset_result["success"]:
            items = dataset_result["items"]
        else:
            logger.warning(f"Failed to fetch dataset items: {dataset_result.get('error')}")

    logger.info(f"Actor run completed for {actor_id} for IP: {request.remote_addr}")
    return jsonify({"success": True, "run": run_data, "results": items})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)