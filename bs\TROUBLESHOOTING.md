# Troubleshooting Guide

This guide helps you resolve common issues with the Apify Actor Runner application.

## Installation Issues

### Python Version Problems

**Problem**: `pip install` fails with version errors
```
ERROR: This package requires Python >=3.7
```

**Solution**:
1. Check your Python version: `python --version`
2. Install Python 3.7+ from [python.org](https://python.org)
3. Use `python3` and `pip3` if you have multiple versions

### Dependency Installation Fails

**Problem**: `pip install -r requirements.txt` fails

**Solutions**:
1. **Update pip**: `pip install --upgrade pip`
2. **Use virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```
3. **Install individually**:
   ```bash
   pip install flask==3.0.0
   pip install requests==2.31.0
   pip install python-dotenv==1.0.0
   ```

## Application Startup Issues

### Port Already in Use

**Problem**: `Address already in use` error

**Solutions**:
1. **Change port**: Edit `app.py` and change `port=5000` to another port
2. **Kill existing process**:
   - Windows: `netstat -ano | findstr :5000` then `taskkill /PID <PID> /F`
   - Mac/Linux: `lsof -ti:5000 | xargs kill -9`

### Module Not Found Errors

**Problem**: `ModuleNotFoundError: No module named 'flask'`

**Solutions**:
1. **Activate virtual environment** if using one
2. **Install missing modules**: `pip install flask requests python-dotenv`
3. **Check Python path**: Ensure you're using the correct Python interpreter

## API Token Issues

### Invalid Token Error

**Problem**: "Invalid API token" or "Authentication failed"

**Solutions**:
1. **Get new token**: Visit [Apify Console](https://console.apify.com/account#/integrations)
2. **Check token format**: Should start with `apify_api_`
3. **Copy carefully**: Ensure no extra spaces or characters
4. **Test manually**: Try the token in Apify's API documentation

### Token Validation Timeout

**Problem**: Token validation takes too long or times out

**Solutions**:
1. **Check internet connection**
2. **Try different network**: Corporate firewalls may block API calls
3. **Increase timeout**: Edit `apify_client.py` and increase `timeout=10`

## Actor Loading Issues

### No Actors Found

**Problem**: "No actors found in your account"

**Solutions**:
1. **Create an actor**: Go to [Apify Console](https://console.apify.com) and create/import an actor
2. **Check permissions**: Ensure your API token has access to actors
3. **Verify account**: Make sure you're using the correct Apify account

### Actor Schema Loading Fails

**Problem**: "Error loading schema" when selecting an actor

**Solutions**:
1. **Check actor status**: Ensure the actor is built and ready
2. **Try different actor**: Some actors may have malformed schemas
3. **Check actor permissions**: Ensure you have access to the actor

## Form Generation Issues

### Missing Input Fields

**Problem**: Form doesn't show expected input fields

**Solutions**:
1. **Check actor schema**: The actor may not have a proper input schema
2. **Try in Apify Console**: Test the actor directly in Apify to see expected inputs
3. **Check browser console**: Look for JavaScript errors

### Field Type Not Supported

**Problem**: Some input fields don't render correctly

**Solutions**:
1. **Check supported types**: See `docs/API.md` for supported schema types
2. **Manual input**: Use text fields for complex types
3. **Report issue**: Complex schemas may need custom handling

## Actor Execution Issues

### Run Timeout

**Problem**: Actor run times out after 2 minutes

**Solutions**:
1. **Use faster actor**: Choose actors that complete quickly
2. **Increase timeout**: Edit `apify_client.py` and increase `waitForFinish` parameter
3. **Run asynchronously**: Modify the code to poll for completion instead

### Run Fails Immediately

**Problem**: Actor run fails with error message

**Solutions**:
1. **Check input data**: Ensure all required fields are provided
2. **Validate input format**: Check data types match schema requirements
3. **Test in Apify Console**: Try running the actor directly in Apify
4. **Check actor logs**: Look at the error details in the response

### No Results Returned

**Problem**: Actor runs successfully but no results shown

**Solutions**:
1. **Check actor behavior**: Some actors don't return dataset results
2. **Look for other outputs**: Check key-value store or other storage
3. **Verify actor logic**: The actor may not be producing expected output

## Browser Issues

### JavaScript Errors

**Problem**: Browser console shows JavaScript errors

**Solutions**:
1. **Refresh page**: Try a hard refresh (Ctrl+F5)
2. **Clear cache**: Clear browser cache and cookies
3. **Try different browser**: Test in Chrome, Firefox, or Safari
4. **Check browser version**: Ensure you're using a modern browser

### Interface Not Responsive

**Problem**: Buttons don't work or interface is frozen

**Solutions**:
1. **Check network tab**: Look for failed API requests
2. **Disable browser extensions**: Ad blockers may interfere
3. **Check JavaScript console**: Look for error messages

## Network Issues

### Connection Refused

**Problem**: Cannot connect to the application

**Solutions**:
1. **Check server status**: Ensure `python app.py` is running
2. **Verify URL**: Make sure you're accessing `http://localhost:5000`
3. **Check firewall**: Ensure port 5000 is not blocked

### API Calls Fail

**Problem**: All API calls return network errors

**Solutions**:
1. **Check internet connection**
2. **Test Apify API directly**: Try `curl https://api.apify.com/v2/users/me -H "Authorization: Bearer YOUR_TOKEN"`
3. **Check proxy settings**: Corporate proxies may interfere
4. **Try different network**: Switch to mobile hotspot for testing

## Performance Issues

### Slow Loading

**Problem**: Application loads slowly

**Solutions**:
1. **Check network speed**: Slow internet affects API calls
2. **Reduce actor list**: If you have many actors, loading may be slow
3. **Close other applications**: Free up system resources

### Memory Issues

**Problem**: Browser becomes unresponsive with large results

**Solutions**:
1. **Limit result size**: Choose actors that return smaller datasets
2. **Increase browser memory**: Close other tabs and applications
3. **Use pagination**: Modify code to paginate large result sets

## Common Error Messages

### "API token is required"
- **Cause**: Empty or missing token
- **Solution**: Enter a valid Apify API token

### "HTTP 401: Unauthorized"
- **Cause**: Invalid or expired token
- **Solution**: Get a new token from Apify Console

### "HTTP 404: Not Found"
- **Cause**: Actor ID doesn't exist or no access
- **Solution**: Check actor ID and permissions

### "HTTP 429: Too Many Requests"
- **Cause**: Rate limit exceeded
- **Solution**: Wait a moment and try again

### "Network error: Failed to fetch"
- **Cause**: Network connectivity issues
- **Solution**: Check internet connection and server status

## Getting Help

### Debug Information to Collect
1. **Browser console errors**: Press F12 and check Console tab
2. **Network requests**: Check Network tab for failed requests
3. **Python error messages**: Check terminal where you ran `python app.py`
4. **System information**: OS, Python version, browser version

### Where to Get Help
1. **Apify Documentation**: [docs.apify.com](https://docs.apify.com)
2. **Apify Community**: [community.apify.com](https://community.apify.com)
3. **GitHub Issues**: If this is an open-source project
4. **Stack Overflow**: Tag questions with `apify` and `python`

### Before Asking for Help
1. **Try the solutions above**
2. **Search for similar issues**
3. **Prepare debug information**
4. **Create a minimal reproduction case**

## Advanced Debugging

### Enable Debug Mode
Add this to your `.env` file:
```
FLASK_DEBUG=1
```

### Verbose Logging
Add logging to `app.py`:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Test API Directly
Use curl to test Apify API:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" https://api.apify.com/v2/users/me
```

### Browser Developer Tools
1. **Console**: Check for JavaScript errors
2. **Network**: Monitor API requests and responses
3. **Elements**: Inspect HTML structure
4. **Application**: Check local storage and cookies
