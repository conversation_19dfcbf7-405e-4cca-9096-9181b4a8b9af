// Global state
let token = '';
let selectedActor = null;
let currentStep = 1;
const totalSteps = 4;

// --- UI Helpers ---

const showLoader = (show) => document.getElementById('loader').classList.toggle('active', show);

function showStep(stepId) {
    document.querySelectorAll('.step').forEach(el => el.classList.remove('active'));
    document.getElementById(stepId).classList.add('active');

    currentStep = { 'auth-step': 1, 'actors-step': 2, 'config-step': 3, 'results-step': 4 }[stepId] || 1;
    document.querySelector('.progress-fill').style.width = `${(currentStep / totalSteps) * 100}%`;
}

function showStatus(id, message, isError = false) {
    const el = document.getElementById(id);
    if (!message) {
        el.textContent = '';
        el.className = 'status';
        return;
    }
    el.textContent = message;
    el.className = `status ${isError ? 'error' : 'success'}`;
}

// --- API Helper ---

async function apiFetch(endpoint, body) {
    try {
        const res = await fetch(endpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(body),
        });
        const data = await res.json();
        if (!res.ok) {
            // Give a default error if the API doesn't provide one
            return { success: false, error: data.error || `Request failed with status ${res.status}` };
        }
        return data;
    } catch (err) {
        console.error(`API call to ${endpoint} failed:`, err);
        return { success: false, error: 'Network error or invalid response.' };
    }
}

// --- Core Logic ---

async function authenticate() {
    token = document.getElementById('api-token').value.trim();
    if (!token) return showStatus('auth-status', 'API token is required.', true);

    showLoader(true);
    showStatus('auth-status', '');
    const data = await apiFetch('/api/test-token', { api_token: token });
    showLoader(false);

    if (data.success) {
        showStatus('auth-status', 'Success! Token is valid.');
        setTimeout(loadActors, 500);
    } else {
        showStatus('auth-status', data.error, true);
    }
}

async function loadActors() {
    showStep('actors-step');
    showLoader(true);
    showStatus('actors-status', '');
    
    const data = await apiFetch('/api/actors', { api_token: token });
    showLoader(false);

    if (data.success) {
        renderActors(data.actors);
        showStatus('actors-status', `Found ${data.actors.length} actors.`);
    } else {
        showStatus('actors-status', data.error, true);
    }
}

function renderActors(actors) {
    const grid = document.getElementById('actors-grid');
    grid.innerHTML = actors.map(actor => `
        <div class="actor-card" onclick='selectActor(${JSON.stringify(actor)})'>
            <h3>${actor.name || 'Unnamed Actor'}</h3>
            <p>${actor.title || 'No description available.'}</p>
        </div>
    `).join('');
}

function selectActor(actor) {
    selectedActor = actor;
    updateSessionStats('actorsViewed');
    loadSchema(actor.id);
}

async function loadSchema(actorId) {
    showStep('config-step');
    showLoader(true);
    showStatus('config-status', '');

    document.getElementById('selected-actor-info').innerHTML = `
        <h3>${selectedActor.name}</h3>
        <p>${selectedActor.title || 'No description.'}</p>
    `;

    const data = await apiFetch(`/api/actor/${actorId}/schema`, { api_token: token });
    showLoader(false);

    if (data.success) {
        buildForm(data.schema);
    } else {
        showStatus('config-status', data.error, true);
        document.getElementById('form-fields').innerHTML = '<p>Could not load input configuration.</p>';
    }
}

function buildForm(schema) {
    const container = document.getElementById('form-fields');
    container.innerHTML = '';
    const fields = schema?.properties || {};

    if (Object.keys(fields).length === 0) {
        container.innerHTML = '<p style="text-align:center;">This actor requires no configuration.</p>';
        return;
    }

    for (const [key, field] of Object.entries(fields)) {
        const isRequired = schema.required?.includes(key);
        const labelText = `${field.title || key}${isRequired ? ' *' : ''}`;
        let inputHtml = '';

        if (field.enum) {
            const options = field.enum.map(opt => `<option value="${opt}">${opt}</option>`).join('');
            inputHtml = `<select name="${key}" ${isRequired ? 'required' : ''}>${!isRequired ? '<option value="">-- Select --</option>' : ''}${options}</select>`;
        } else if (field.type === 'boolean') {
            inputHtml = `<input type="checkbox" name="${key}" ${field.default ? 'checked' : ''} style="width: auto; height: 1.2rem;">`;
        } else {
            const type = (field.type === 'integer' || field.type === 'number') ? 'number' : 'text';
            inputHtml = `<input type="${type}" name="${key}" placeholder="${field.placeholder || ''}" ${field.default !== undefined ? `value="${field.default}"` : ''} ${isRequired ? 'required' : ''}>`;
        }

        container.innerHTML += `
            <div class="field-group">
                <label class="tooltip">${labelText}<span class="tooltiptext">${field.description || 'No description.'}</span></label>
                ${inputHtml}
            </div>
        `;
    }
}

async function executeActor() {
    const form = document.getElementById('input-form');
    const formData = new FormData(form);
    const inputData = {};

    // *** CRITICAL: Correctly parse form data ***
    // This handles numbers (including 0), booleans, and empty strings correctly.
    for (const [key, val] of formData.entries()) {
        const inputEl = form.querySelector(`[name="${key}"]`);
        if (inputEl.type === 'checkbox') {
            inputData[key] = inputEl.checked;
        } else if (inputEl.type === 'number') {
            // Don't submit if empty, otherwise parse as a number. Allows 0.
            if (val.trim() !== '') inputData[key] = parseFloat(val);
        } else if (val.trim() !== '') {
            inputData[key] = val;
        }
    }

    showLoader(true);
    showStatus('config-status', '');

    const data = await apiFetch(`/api/actor/${selectedActor.id}/run`, {
        api_token: token,
        input: inputData,
    });
    
    showLoader(false);

    if (data.success) {
        updateSessionStats('runsExecuted');
        showResults(data.run, data.results);
    } else {
        showStatus('config-status', data.error, true);
    }
}

function showResults(run, results) {
    showStep('results-step');

    const statusEl = document.getElementById('run-status');
    statusEl.textContent = run.status;
    statusEl.className = `run-status ${run.status === 'SUCCEEDED' ? 'success' : 'failed'}`;

    const content = document.getElementById('results-content');
    const runTime = run.stats?.runTimeSecs?.toFixed(2) || '?';
    
    content.innerHTML = `
        <dl>
            <dt>Status</dt><dd>${run.status}</dd>
            <dt>Started</dt><dd>${new Date(run.startedAt).toLocaleString()}</dd>
            <dt>Finished</dt><dd>${new Date(run.finishedAt).toLocaleString()}</dd>
            <dt>Duration</dt><dd>${runTime}s</dd>
            <dt>Items</dt><dd>${results?.length ?? 'N/A'}</dd>
        </dl>
        <h3>Results JSON</h3>
        <pre>${JSON.stringify(results || { message: 'No items in dataset.' }, null, 2)}</pre>
    `;
}

function restart() {
    token = '';
    selectedActor = null;
    document.getElementById('api-token').value = '';
    showStatus('auth-status', '');
    showStep('auth-step');
}

// --- Session Tracking ---
const session = {
    start: Date.now(),
    actorsViewed: new Set(),
    runsExecuted: 0,
};

function updateSessionStats(action) {
    if (action === 'actorsViewed' && selectedActor) session.actorsViewed.add(selectedActor.id);
    if (action === 'runsExecuted') session.runsExecuted++;

    const secs = Math.floor((Date.now() - session.start) / 1000);
    document.getElementById('session-duration').textContent = `${Math.floor(secs / 60)}m ${secs % 60}s`;
    document.getElementById('actors-viewed').textContent = session.actorsViewed.size;
    document.getElementById('runs-executed').textContent = session.runsExecuted;

    const statsBox = document.getElementById('session-stats');
    if (session.actorsViewed.size > 0 || session.runsExecuted > 0) {
        statsBox.style.display = 'block';
    }
}

setInterval(updateSessionStats, 5000); // Periodically update time